import { AccountType } from '../../../types/websocket/enums'

class WebSocketAuth {
  private static instance: WebSocketAuth
  private authPayload: WSAuthPayload | null = null

  constructor() {
    this.authPayload = {
      session: import.meta.env.MAIN_VITE_SESSION_ID,
      isDemo: AccountType.Demo,
      uid: import.meta.env.MAIN_VITE_USER_ID,
      platform: 2,
      isFastHistory: true
    }
  }

  public static getInstance(): WebSocketAuth {
    if (!WebSocketAuth.instance) {
      WebSocketAuth.instance = new WebSocketAuth()
    }
    return WebSocketAuth.instance
  }

  getAuthPayload(): WSAuthPayload {
    return this.authPayload!
  }
}

export default WebSocketAuth
