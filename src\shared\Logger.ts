import pc from 'picocolors'

type LogLevel = 'info' | 'warn' | 'error' | 'debug' | 'success'

interface LoggerOptions {
  showLogLevel?: boolean
  showLineNumber?: boolean
}

export class Logger {
  private scope: string
  private options: LoggerOptions

  constructor(scope?: string, options?: LoggerOptions) {
    this.scope = scope ?? 'app'
    this.options = {
      showLogLevel: true,
      showLineNumber: true,
      ...options
    }
  }

  private format(level: LogLevel, msg: string): string {
    const timestamp = new Date().toISOString()

    const parts = [
      pc.gray(`[${timestamp}]`),
      pc.cyan(`[${this.scope}]`),
      this.options.showLogLevel ? `${this.getColor(level, `[${level.toUpperCase()}]`)}` : null,
      this.getColor(level, msg)
    ].filter(Boolean)

    return parts.join(' ')
  }

  private log(level: LogLevel, message: string, ...args: unknown[]): void {
    console.log(this.format(level, message), ...args)
  }

  info(msg: string, ...args: unknown[]): void {
    this.log('info', msg, ...args)
  }

  warn(msg: string, ...args: unknown[]): void {
    this.log('warn', msg, ...args)
  }

  error(msg: string, ...args: unknown[]): void {
    this.log('error', msg, ...args)
  }

  debug(msg: string, ...args: unknown[]): void {
    if (this.options.showLineNumber) {
      const error = new Error()
      const lineNumber = error.stack?.split('\n')[2].split(':')[1]
      const fileName = error.stack?.split('\n')[2].split(':')[0].split('\\').pop()
      msg = `${msg}
${pc.bold(pc.cyan(`${fileName} on (Line: ${lineNumber})`))}`
    }
    this.log('debug', msg, ...args)
  }

  success(msg: string, ...args: unknown[]): void {
    this.log('success', msg, ...args)
  }

  private getColor(level: LogLevel, text: string): string {
    switch (level) {
      case 'info':
        return pc.blue(text)
      case 'warn':
        return pc.yellow(text)
      case 'error':
        return pc.red(text)
      case 'debug':
        return pc.magenta(text)
      case 'success':
        return pc.green(text)
      default:
        return pc.white(text)
    }
  }

  createChild(scope: string, options?: LoggerOptions): Logger {
    return new Logger(`${this.scope}:${scope}`, {
      ...this.options,
      ...options
    })
  }
}
