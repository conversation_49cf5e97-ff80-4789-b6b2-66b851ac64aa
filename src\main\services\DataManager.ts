import { CandleData, MarketData, ProcessedMarketData, TickData } from '../../types/trading'

class DataManager {
  private priceData: Map<string, MarketData[]> = new Map()
  private marketDataStore: Map<string, ProcessedMarketData> = new Map()

  constructor() {
    console.log(`DataManager initialized`)
  }

  /**
   * Retrieves the historical price data for a given asset symbol.
   * @param assetSymbol The symbol of the asset.
   * @returns An array of MarketData points or null if not found.
   */
  public getPriceHistory(assetSymbol: string): MarketData[] | null {
    return this.priceData.get(assetSymbol) || null
  }

  /**
   * Retrieves the processed market data, including candles, for a given asset symbol.
   * @param assetSymbol The symbol of the asset.
   * @returns The ProcessedMarketData object or null if not found.
   */
  public getProcessedMarketData(assetSymbol: string): ProcessedMarketData | null {
    return this.marketDataStore.get(assetSymbol) || null
  }

  /**
   * Collects and processes market data, including history and candles.
   * @param assetSymbol The symbol of the asset.
   * @param data The raw data object from the source.
   */
  public collectMarketData(
    assetSymbol: string,
    data: { candles?: Partial<CandleData>[]; history?: [number, number][] }
  ): void {
    if (!assetSymbol) return

    let marketData = this.marketDataStore.get(assetSymbol)

    if (!marketData) {
      marketData = {
        symbol: assetSymbol,
        candles: [],
        latestTick: null,
        lastAnalysis: null
      }
      this.marketDataStore.set(assetSymbol, marketData)
    }

    if (data.candles) {
      this.parseFastCandleData(assetSymbol, data.candles)
    }

    if (data.history) {
      this.parseFastHistoryData(assetSymbol, data.history)
    }
  }

  /**
   * Updates the market data with a new price point.
   * @param data The market data to add.
   * @param timestamp The timestamp of the data.
   */
  public updateMarketData(
    data: Omit<MarketData, 'timestamp' | 'volume'> & { volume?: number },
    timestamp?: Date
  ): void {
    const marketData: MarketData = {
      ...data,
      timestamp: timestamp || new Date(),
      volume: data.volume // Volume should be provided, not randomized
    }

    if (!this.priceData.has(data.symbol)) {
      this.priceData.set(data.symbol, [])
    }

    this.addPriceData(data.symbol, marketData)
  }

  /**
   * Parses and stores incoming candle data.
   * @param assetSymbol The symbol of the asset.
   * @param candles The array of raw candle data.
   */
  private parseFastCandleData(assetSymbol: string, candles: Partial<CandleData>[]): void {
    const marketData = this.marketDataStore.get(assetSymbol)
    if (!marketData || !Array.isArray(candles)) return

    const newCandles: CandleData[] = candles
      .filter(
        (c) =>
          c.timestamp != null &&
          c.open != null &&
          c.high != null &&
          c.low != null &&
          c.close != null
      )
      .map((candle) => ({
        timestamp: candle.timestamp!,
        open: candle.open!,
        high: candle.high!,
        low: candle.low!,
        close: candle.close!,
        volume: candle.volume
      }))

    newCandles.sort((a, b) => a.timestamp - b.timestamp)

    for (const candle of newCandles) {
      const index = marketData.candles.findIndex((c) => c.timestamp === candle.timestamp)
      if (index >= 0) {
        marketData.candles[index] = candle
      } else {
        marketData.candles.push(candle)
      }
    }

    marketData.candles.sort((a, b) => a.timestamp - b.timestamp)
    if (marketData.candles.length > 500) {
      marketData.candles = marketData.candles.slice(-500)
    }
  }

  /**
   * Parses and stores incoming historical tick data.
   * @param assetSymbol The symbol of the asset.
   * @param history The array of raw historical data.
   */
  private parseFastHistoryData(assetSymbol: string, history: [number, number][]): void {
    const marketData = this.marketDataStore.get(assetSymbol)
    if (!marketData || !Array.isArray(history)) return

    const latestTickData = history[history.length - 1]
    if (latestTickData && latestTickData.length >= 2) {
      const tick: TickData = {
        asset: assetSymbol,
        timestamp: latestTickData[0],
        price: latestTickData[1]
      }
      marketData.latestTick = tick

      this.updateMarketData(
        {
          symbol: assetSymbol,
          price: tick.price
        },
        new Date(tick.timestamp * 1000)
      )
    }
  }

  /**
   * Adds a new price data point to the history.
   * @param assetSymbol The symbol of the asset.
   * @param data The market data point to add.
   */
  private addPriceData(assetSymbol: string, data: MarketData): void {
    const existing = this.priceData.get(assetSymbol) || []
    existing.push(data)

    if (existing.length > 1000) {
      existing.splice(0, existing.length - 1000)
    }

    this.priceData.set(assetSymbol, existing)
  }
}

export default DataManager
