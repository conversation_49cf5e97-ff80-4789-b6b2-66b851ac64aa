export interface PriceData {
  timestamp: number
  price: number
}

export interface Candle {
  timestamp: number
  open: number
  close: number
  high: number
  low: number
}

export interface MarketData {
  asset: string
  period: number
  history: Array<[number, number]>
  candles: Array<[number, number, number, number, number]>
}

export interface TradeSignal {
  type: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  timestamp: number
  price: number
  suggestedDuration?: number // in seconds
  reason: string
}

export interface MACDResult {
  macd: number
  signal: number
  histogram: number
}

export class SignalGenerator {
  private readonly fastPeriod: number = 12
  private readonly slowPeriod: number = 26
  private readonly signalPeriod: number = 9

  /**
   * Calculate Exponential Moving Average
   */
  private calculateEMA(prices: number[], period: number): number[] {
    if (prices.length < period) {
      throw new Error(`Not enough data points for EMA calculation. Need at least ${period} points.`)
    }

    const ema: number[] = []
    const multiplier = 2 / (period + 1)

    // Calculate initial SMA as the first EMA value
    let sum = 0
    for (let i = 0; i < period; i++) {
      sum += prices[i]
    }
    ema[period - 1] = sum / period

    // Calculate subsequent EMA values
    for (let i = period; i < prices.length; i++) {
      ema[i] = (prices[i] - ema[i - 1]) * multiplier + ema[i - 1]
    }

    return ema
  }

  /**
   * Calculate MACD indicator
   */
  private calculateMACD(prices: number[]): MACDResult[] {
    const fastEMA = this.calculateEMA(prices, this.fastPeriod)
    const slowEMA = this.calculateEMA(prices, this.slowPeriod)

    const macdLine: number[] = []

    // Calculate MACD line (fast EMA - slow EMA)
    for (let i = this.slowPeriod - 1; i < prices.length; i++) {
      if (fastEMA[i] !== undefined && slowEMA[i] !== undefined) {
        macdLine.push(fastEMA[i] - slowEMA[i])
      }
    }

    // Calculate Signal line (EMA of MACD)
    const signalLine = this.calculateEMA(macdLine, this.signalPeriod)

    const results: MACDResult[] = []

    // Calculate histogram and create results
    for (let i = this.signalPeriod - 1; i < macdLine.length; i++) {
      if (signalLine[i] !== undefined) {
        results.push({
          macd: macdLine[i],
          signal: signalLine[i],
          histogram: macdLine[i] - signalLine[i]
        })
      }
    }

    return results
  }

  /**
   * Analyze volatility to suggest trade duration
   */
  private analyzeDuration(prices: number[], period: number): number {
    // Calculate average true range for volatility
    const priceChanges: number[] = []

    for (let i = 1; i < prices.length; i++) {
      priceChanges.push(Math.abs(prices[i] - prices[i - 1]))
    }

    const avgChange = priceChanges.reduce((a, b) => a + b, 0) / priceChanges.length
    const avgPricePercent = (avgChange / (prices.reduce((a, b) => a + b, 0) / prices.length)) * 100

    // Suggest duration based on volatility
    if (avgPricePercent < 0.01) {
      // Low volatility - longer duration
      return period * 10 // 10 candles
    } else if (avgPricePercent < 0.05) {
      // Medium volatility
      return period * 5 // 5 candles
    } else {
      // High volatility - shorter duration
      return period * 3 // 3 candles
    }
  }

  /**
   * Generate trade signal based on MACD strategy
   */
  generateSignal(marketData: MarketData, customDuration?: number): TradeSignal {
    // Extract prices from history
    const prices = marketData.history.map(([, price]) => price)

    if (prices.length < this.slowPeriod + this.signalPeriod) {
      return {
        type: 'HOLD',
        confidence: 0,
        timestamp: Date.now(),
        price: prices[prices.length - 1],
        reason: 'Insufficient data for MACD calculation'
      }
    }

    // Calculate MACD
    const macdResults = this.calculateMACD(prices)

    if (macdResults.length < 2) {
      return {
        type: 'HOLD',
        confidence: 0,
        timestamp: Date.now(),
        price: prices[prices.length - 1],
        reason: 'Insufficient MACD data'
      }
    }

    // Get current and previous MACD values
    const currentMACD = macdResults[macdResults.length - 1]
    const previousMACD = macdResults[macdResults.length - 2]

    // Detect crossovers
    const bullishCrossover =
      previousMACD.macd <= previousMACD.signal && currentMACD.macd > currentMACD.signal
    const bearishCrossover =
      previousMACD.macd >= previousMACD.signal && currentMACD.macd < currentMACD.signal

    // Calculate confidence based on histogram strength
    const histogramStrength = Math.abs(currentMACD.histogram)
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length
    const confidence = Math.min(100, (histogramStrength / avgPrice) * 10000)

    // Determine trade duration
    const duration = customDuration || this.analyzeDuration(prices, marketData.period)

    const currentPrice = prices[prices.length - 1]
    const currentTimestamp = marketData.history[marketData.history.length - 1][0]

    if (bullishCrossover) {
      return {
        type: 'BUY',
        confidence: confidence,
        timestamp: currentTimestamp,
        price: currentPrice,
        suggestedDuration: duration,
        reason: `MACD bullish crossover detected. MACD: ${currentMACD.macd.toFixed(4)}, Signal: ${currentMACD.signal.toFixed(4)}`
      }
    } else if (bearishCrossover) {
      return {
        type: 'SELL',
        confidence: confidence,
        timestamp: currentTimestamp,
        price: currentPrice,
        suggestedDuration: duration,
        reason: `MACD bearish crossover detected. MACD: ${currentMACD.macd.toFixed(4)}, Signal: ${currentMACD.signal.toFixed(4)}`
      }
    } else {
      // Check trend strength
      const trend = currentMACD.macd > currentMACD.signal ? 'bullish' : 'bearish'

      return {
        type: 'HOLD',
        confidence: confidence,
        timestamp: currentTimestamp,
        price: currentPrice,
        suggestedDuration: duration,
        reason: `No crossover detected. Current trend: ${trend} (Histogram: ${currentMACD.histogram.toFixed(4)})`
      }
    }
  }

  /**
   * Get detailed MACD analysis
   */
  getMACDAnalysis(marketData: MarketData): {
    current: MACDResult | null
    previous: MACDResult | null
    trend: 'bullish' | 'bearish' | 'neutral'
    strength: 'weak' | 'moderate' | 'strong'
  } {
    const prices = marketData.history.map(([, price]) => price)

    if (prices.length < this.slowPeriod + this.signalPeriod) {
      return {
        current: null,
        previous: null,
        trend: 'neutral',
        strength: 'weak'
      }
    }

    const macdResults = this.calculateMACD(prices)

    if (macdResults.length < 2) {
      return {
        current: null,
        previous: null,
        trend: 'neutral',
        strength: 'weak'
      }
    }

    const current = macdResults[macdResults.length - 1]
    const previous = macdResults[macdResults.length - 2]

    // Determine trend
    let trend: 'bullish' | 'bearish' | 'neutral' = 'neutral'
    if (current.macd > current.signal && current.histogram > previous.histogram) {
      trend = 'bullish'
    } else if (current.macd < current.signal && current.histogram < previous.histogram) {
      trend = 'bearish'
    }

    // Determine strength
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length
    const normalizedHistogram = (Math.abs(current.histogram) / avgPrice) * 100

    let strength: 'weak' | 'moderate' | 'strong' = 'weak'
    if (normalizedHistogram > 0.1) {
      strength = 'strong'
    } else if (normalizedHistogram > 0.05) {
      strength = 'moderate'
    }

    return {
      current,
      previous,
      trend,
      strength
    }
  }
}
