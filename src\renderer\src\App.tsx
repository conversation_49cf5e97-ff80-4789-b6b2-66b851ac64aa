import Versions from './components/Versions'

function App(): React.JSX.Element {
  const handleClick = (): void => {
    // window.api.send('ping')

    window.api.invoke('ws:selectAsset', 'AUDUSD_otc')
    // const requestId = Date.now()
    // const timeframe = 'M1'
    // const timePeriod = extractTimePeriod(timeframe)

    // window.api.invoke('changeSymbol', { asset, 'USDJPY_otc': period: timeframeToSeconds(timeframe) })
    // window.api.invoke('ws:emit', 'loadHistoryPeriod', {
    //   asset: 'USDJPY_otc',
    //   index: requestId,
    //   offset: 9000,
    //   period: 60,
    //   time: Math.floor(new Date().getTime() / 1000)
    // })
  }

  const handleGenerateSignal = (): void => {
    window.api.invoke('bot:start', 'AUDUSD_otc')
  }

  return (
    <>
      <div className="flex flex-row gap-2">
        <button
          className="btn px-4 py-2 cursor-pointer rounded-sm border border-amber-400"
          onClick={handleClick}
        >
          Select Asset
        </button>
        <button
          className="btn px-4 py-2 cursor-pointer rounded-sm border border-amber-400"
          onClick={handleGenerateSignal}
        >
          Start Bot
        </button>
      </div>

      <Versions></Versions>
    </>
  )
}

export default App
