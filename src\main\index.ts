import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import WebSocketClient from './services/websocket/WebSocketClient'
import { Logger } from '../shared/Logger'

let wsClient: WebSocketClient | null = null
let mainWindow: BrowserWindow | null = null

function createWindow(): void {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    alwaysOnTop: true,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow?.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // Handle window closed event
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  wsClient = WebSocketClient.getInstance()
  const logger = new Logger('MAIN')

  /**
   * Global invokable IPC channel
   * Allows the renderer process to emit events to the WebSocket server.
   * Example: window.api.invoke('ws:emit', 'openOrder', ...args)
   */
  ipcMain.handle('ws:emit', (_, event: string, ...args: unknown[]) => {
    logger.info(`Emitting event: ${event}`)
    if (wsClient) {
      wsClient.emit(event, ...args)
    } else {
      logger.warn('WebSocket client is not initialized')
    }
  })

  // Renderer sends ping (e.g., window.api.send('ping'))
  ipcMain.on('ping', () => console.log('pong'))

  // ===========================================================
  // Invokable IPC channels                                    =
  // ===========================================================

  ipcMain.handle(`ws:selectAsset`, (_, assetSymbol: string) => {
    if (wsClient) {
      wsClient.selectAsset(assetSymbol)
    } else {
      const logger = new Logger('MAIN')
      logger.warn('WebSocket client is not initialized')
    }
  })

  ipcMain.handle('signal:generate', (_, assetSymbol: string) => {
    if (wsClient) {
      wsClient.selectAsset(assetSymbol, true)
    } else {
      logger.error(`WebSocket client is not initialized`)
    }
  })

  ipcMain.handle('bot:start', (_, assetSymbol: string) => {
    if (wsClient) {
      wsClient.setAutoTrading(true)
      wsClient.selectAsset(assetSymbol)
    } else {
      logger.error(`WebSocket client is not initialized`)
    }
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Cleanup function to free up resources
async function cleanup(): Promise<void> {
  const logger = new Logger('MAIN')
  logger.info('Performing cleanup before app exit...')

  try {
    // Disconnect WebSocket client
    if (wsClient) {
      logger.info('Disconnecting WebSocket client...')
      wsClient.disconnect()
      wsClient = null
    }

    // Remove all IPC handlers
    ipcMain.removeAllListeners()
    ipcMain.removeHandler('ws:emit')
    ipcMain.removeHandler('ws:selectAsset')

    // Clear any remaining references
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.removeAllListeners()
      mainWindow = null
    }

    logger.info('Cleanup completed successfully')
  } catch (error) {
    logger.error('Error during cleanup:', error)
  }
}

// Handle process termination signals (for development)
let isCleaningUp = false

async function handleTerminationSignal(signal: string): Promise<void> {
  const logger = new Logger('MAIN')

  if (isCleaningUp) {
    logger.info(`Already cleaning up, ignoring ${signal}`)
    return
  }

  isCleaningUp = true
  logger.info(`Received ${signal}, performing cleanup...`)

  await cleanup()

  // Exit the process
  process.exit(0)
}

// Register signal handlers for development (Ctrl+C, etc.)
process.on('SIGINT', () => handleTerminationSignal('SIGINT'))
process.on('SIGTERM', () => handleTerminationSignal('SIGTERM'))
process.on('SIGHUP', () => handleTerminationSignal('SIGHUP'))

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
  const logger = new Logger('MAIN')
  logger.error('Uncaught exception:', error)
  await handleTerminationSignal('uncaughtException')
})

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  const logger = new Logger('MAIN')
  logger.error('Unhandled rejection at:', promise, 'reason:', reason)
  await handleTerminationSignal('unhandledRejection')
})

// Handle app termination events
app.on('before-quit', async (event) => {
  if (isCleaningUp) {
    return
  }

  const logger = new Logger('MAIN')
  logger.info('App is about to quit, performing cleanup...')

  // Prevent default quit temporarily
  event.preventDefault()

  isCleaningUp = true

  // Perform cleanup
  await cleanup()

  // Now quit the app
  app.exit(0)
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', async () => {
  if (process.platform !== 'darwin') {
    if (!isCleaningUp) {
      isCleaningUp = true
      await cleanup()
    }
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
