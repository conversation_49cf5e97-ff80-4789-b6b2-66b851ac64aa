import DataManager from './DataManager'
import RsiIndicator from './trader/indicators/RsiIndicator'
import BollingerBandsIndicator from './trader/indicators/BollingerBandsIndicator'
import MovingAverageIndicator from './trader/indicators/MovingAverageIndicator'
import StochasticOscillatorIndicator from './trader/indicators/StochasticOscillatorIndicator'
import { calculateSupportAndResistance } from './trader/analysis/supportResistance'
import { generateTradingRecommendations } from './trader/analysis/recommendations'
import {
  AnalysisResult,
  BollingerBandsResult,
  MACDSettings,
  MovingAverageResult,
  RSIResult,
  RSISettings,
  StochasticOscillatorResult,
  StochasticOscillatorSettings,
  BollingerBandsSettings,
  MovingAverageSettings
} from '../../types/trading'

interface DataAnalyzerSettings {
  rsi: RSISettings
  bollingerBands: BollingerBandsSettings
  macd: MACDSettings
  stochasticOscillator: StochasticOscillatorSettings
  movingAverage: MovingAverageSettings
  signalWeights: {
    rsi: number
    bollingerBands: number
    macd: number
    stochasticOscillator: number
    movingAverage: number
  }
  minimumConfidenceLevel: number
}

class DataAnalyzer {
  private settings: DataAnalyzerSettings
  private dataManager: DataManager
  private rsiIndicator: RsiIndicator
  private bollingerBands: BollingerBandsIndicator
  private movingAverage: MovingAverageIndicator
  private stochasticOscillator: StochasticOscillatorIndicator
  private lastAnalysis: Map<string, AnalysisResult> = new Map()

  constructor(dataManager: DataManager, settings: Partial<DataAnalyzerSettings> = {}) {
    this.settings = {
      rsi: { period: 14, overbought: 70, oversold: 30 },
      bollingerBands: { period: 20, stdDev: 2 },
      macd: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
      stochasticOscillator: { period: 14, kPeriod: 3, dPeriod: 3 },
      movingAverage: { period: 50, type: 'simple' },
      signalWeights: {
        rsi: 0.25,
        bollingerBands: 0.25,
        macd: 0.15,
        stochasticOscillator: 0.15,
        movingAverage: 0.2
      },
      minimumConfidenceLevel: 0.5,
      ...settings
    }

    this.dataManager = dataManager
    this.rsiIndicator = new RsiIndicator(this.settings.rsi)
    this.bollingerBands = new BollingerBandsIndicator(this.settings.bollingerBands)
    this.movingAverage = new MovingAverageIndicator(this.settings.movingAverage)
    this.stochasticOscillator = new StochasticOscillatorIndicator(
      this.settings.stochasticOscillator
    )
  }

  /**
   * Performs a comprehensive technical analysis for a given asset.
   * @param assetSymbol The symbol of the asset to analyze.
   * @returns An AnalysisResult object or null if analysis cannot be performed.
   */
  public analyze = (assetSymbol: string): AnalysisResult | null => {
    const marketDataHistory = this.dataManager.getPriceHistory(assetSymbol)

    if (!marketDataHistory || marketDataHistory.length < 20) return null

    const priceHistory = marketDataHistory.map((data) => data.price)
    const currentData = marketDataHistory[marketDataHistory.length - 1]

    // Calculate individual indicators
    const rsiResult = this.rsiIndicator.calculate(priceHistory)
    const bollingerBandsResult = this.bollingerBands.calculate(priceHistory)
    const movingAverageResult = this.movingAverage.calculate(priceHistory)
    const stochasticOscillatorResult = this.stochasticOscillator.calculate(priceHistory)

    // Combine Signals
    const combinedAnalysis = this.combineSignals({
      rsi: rsiResult,
      bollingerBands: bollingerBandsResult,
      movingAverage: movingAverageResult,
      stochasticOscillator: stochasticOscillatorResult
    })

    // Assess market conditions
    const marketCondition = this.assessMarketCondition(bollingerBandsResult, movingAverageResult)

    const volatility = this.assessVolatility(bollingerBandsResult)
    const trend = this.assessTrend(movingAverageResult, priceHistory)

    // Calculate support and resistance
    const { supportLevel, resistanceLevel } = calculateSupportAndResistance(priceHistory)

    // Generate trading recommendations
    const tradingRecommendations = generateTradingRecommendations(
      currentData.price,
      combinedAnalysis,
      volatility,
      supportLevel,
      resistanceLevel
    )

    const result: AnalysisResult = {
      timestamp: new Date(),
      symbol: currentData.symbol,
      currentPrice: currentData.price,
      rsi: rsiResult,
      bollingerBands: bollingerBandsResult,
      movingAverage: movingAverageResult,
      stochasticOscillator: stochasticOscillatorResult,
      overallSignal: combinedAnalysis.overallSignal,
      confidence: combinedAnalysis.confidence,
      signalStrength: combinedAnalysis.signalStrength,
      bullishSignals: combinedAnalysis.bullishSignals,
      bearishSignals: combinedAnalysis.bearishSignals,
      neutralSignals: combinedAnalysis.neutralSignals,
      marketCondition,
      volatility,
      trend,
      supportLevel,
      resistanceLevel,
      ...tradingRecommendations
    }

    this.lastAnalysis.set(assetSymbol, result)

    return result
  }

  private combineSignals = (results: {
    rsi: RSIResult | null
    bollingerBands: BollingerBandsResult | null
    movingAverage: MovingAverageResult | null
    stochasticOscillator: StochasticOscillatorResult | null
  }): {
    overallSignal: 'STRONG_BUY' | 'BUY' | 'SELL' | 'HOLD' | 'STRONG_SELL'
    confidence: number
    signalStrength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG'
    bullishSignals: number
    bearishSignals: number
    neutralSignals: number
  } => {
    interface Signal {
      indicator: string
      signal: 'BUY' | 'SELL' | 'HOLD'
      weight: number
      confidence: number
    }

    const signals: Signal[] = []
    let totalWeight = 0

    if (results.rsi) {
      signals.push({
        indicator: 'RSI',
        signal: results.rsi.signal,
        weight: this.settings.signalWeights.rsi,
        confidence: this.getStrengthScore(results.rsi.strength)
      })
      totalWeight += this.settings.signalWeights.rsi
    }

    if (results.bollingerBands) {
      signals.push({
        indicator: 'Bollinger Bands',
        signal: results.bollingerBands.signal,
        weight: this.settings.signalWeights.bollingerBands,
        confidence: this.getStrengthScore(results.bollingerBands.strength)
      })
      totalWeight += this.settings.signalWeights.bollingerBands
    }

    if (results.movingAverage) {
      signals.push({
        indicator: 'Moving Average',
        signal: results.movingAverage.signal,
        weight: this.settings.signalWeights.movingAverage,
        confidence: this.getStrengthScore(results.movingAverage.strength)
      })
      totalWeight += this.settings.signalWeights.movingAverage
    }

    if (results.stochasticOscillator) {
      signals.push({
        indicator: 'Stochastic Oscillator',
        signal: results.stochasticOscillator.signal,
        weight: this.settings.signalWeights.stochasticOscillator,
        confidence: this.getStrengthScore(results.stochasticOscillator.strength)
      })
      totalWeight += this.settings.signalWeights.stochasticOscillator
    }

    // Calculate weighted signal score
    let bullishScore = 0
    let bearishScore = 0
    let neutralScore = 0

    signals.forEach((signal) => {
      const weightedConfidence = signal.weight * signal.confidence

      if (signal.signal === 'BUY') {
        bullishScore += weightedConfidence
      } else if (signal.signal === 'SELL') {
        bearishScore += weightedConfidence
      } else {
        neutralScore += weightedConfidence
      }
    })

    const bullishSignals = signals.filter((signal) => signal.signal === 'BUY').length
    const bearishSignals = signals.filter((signal) => signal.signal === 'SELL').length
    const neutralSignals = signals.filter((signal) => signal.signal === 'HOLD').length

    let overallSignal: 'STRONG_BUY' | 'BUY' | 'SELL' | 'HOLD' | 'STRONG_SELL'
    let signalStrength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG' = 'WEAK'

    const totalSignalStrength = bullishScore + bearishScore + neutralScore
    const confidence = totalWeight > 0 ? totalSignalStrength / totalWeight : 0

    if (bullishScore > bearishScore && bullishScore > neutralScore) {
      if (bullishScore >= bearishScore * 1.5 && confidence > 0.6) {
        overallSignal = 'STRONG_BUY'
        signalStrength = 'VERY_STRONG'
      } else {
        overallSignal = 'BUY'
        signalStrength = confidence > 0.7 ? 'STRONG' : 'MODERATE'
      }
    } else if (bearishScore > bullishScore && bearishScore > neutralScore) {
      if (bearishScore >= bullishScore * 1.5 && confidence > 0.6) {
        overallSignal = 'STRONG_SELL'
        signalStrength = 'VERY_STRONG'
      } else {
        overallSignal = 'SELL'
        signalStrength = confidence > 0.7 ? 'STRONG' : 'MODERATE'
      }
    } else {
      overallSignal = 'HOLD'
      signalStrength = 'WEAK'
    }

    if (confidence < this.settings.minimumConfidenceLevel) {
      overallSignal = 'HOLD'
      signalStrength = 'WEAK'
    }

    return {
      overallSignal,
      confidence: Number(confidence.toFixed(3)),
      signalStrength,
      bullishSignals,
      bearishSignals,
      neutralSignals
    }
  }

  private getStrengthScore = (strength: 'WEAK' | 'MODERATE' | 'STRONG'): number => {
    switch (strength) {
      case 'WEAK':
        return 0.3
      case 'MODERATE':
        return 0.7
      case 'STRONG':
        return 1.0
      default:
        return 0.3
    }
  }

  private assessMarketCondition = (
    bollingerBands: BollingerBandsResult | null,
    movingAverage: MovingAverageResult | null
  ): 'TRENDING' | 'RANGING' | 'VOLATILE' | 'BREAKOUT' => {
    if (!bollingerBands || !movingAverage) {
      return 'RANGING'
    }

    const isSqueeze = bollingerBands.squeeze
    const isTrending = movingAverage.trend !== 'SIDEWAYS'
    const hasBreakout = movingAverage.crossoverType !== 'NONE'

    if (hasBreakout) {
      return 'BREAKOUT'
    }
    if (isSqueeze) {
      return 'RANGING'
    }
    if (bollingerBands.bandwidth > 4) {
      return 'VOLATILE'
    }
    if (isTrending) {
      return 'TRENDING'
    }
    return 'RANGING'
  }

  private assessVolatility = (
    bollingerBands: BollingerBandsResult | null
  ): 'LOW' | 'MODERATE' | 'HIGH' | 'EXTREME' => {
    if (!bollingerBands) return 'MODERATE'

    if (bollingerBands.bandwidth < 1) {
      return 'LOW'
    } else if (bollingerBands.bandwidth < 3) {
      return 'MODERATE'
    } else if (bollingerBands.bandwidth < 6) {
      return 'HIGH'
    } else {
      return 'EXTREME'
    }
  }

  private assessTrend = (
    movingAverage: MovingAverageResult | null,
    priceHistory: number[]
  ): 'STRONG_UPTREND' | 'UPTREND' | 'SIDEWAYS' | 'DOWNTREND' | 'STRONG_DOWNTREND' => {
    if (!movingAverage || priceHistory.length < 20) return 'SIDEWAYS'

    const recentPrices = priceHistory.slice(-10)
    const olderPrices = priceHistory.slice(-20, -10)

    if (recentPrices.length < 10 || olderPrices.length < 10) return 'SIDEWAYS'

    const recentAvg = recentPrices.reduce((sum, p) => sum + p, 0) / recentPrices.length
    const olderAvg = olderPrices.reduce((sum, p) => sum + p, 0) / olderPrices.length

    const trendStrength = ((recentAvg - olderAvg) / olderAvg) * 100

    if (movingAverage.trend === 'UPTREND') {
      return trendStrength > 1 ? 'STRONG_UPTREND' : 'UPTREND'
    } else if (movingAverage.trend === 'DOWNTREND') {
      return trendStrength < -1 ? 'STRONG_DOWNTREND' : 'DOWNTREND'
    } else {
      return 'SIDEWAYS'
    }
  }
}

export default DataAnalyzer
