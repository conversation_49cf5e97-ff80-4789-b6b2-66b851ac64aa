import { AccountType } from './enums'

declare global {
  interface WSAccountBalance {
    balance: number
    isDemo: AccountType
  }

  interface WSAuthPayload {
    session: string
    isDemo: AccountType
    uid: number
    platform: number
    isFastHistory: boolean
  }

  interface ChartSettings {
    chartID: string
    chartType: number
    chartPeriod: number
    candlesTimer: boolean
    symbol: string
    demoDealAmount: number
    liveDealAmount: number
    enabledTradeMonitor: boolean
    enabledRatingWidget: boolean
    isVisible: boolean
    fastTimeframe: number
    enabledAutoscroll: boolean
    enabledGridSnap: boolean
    minimizedTradePanel: boolean
    fastCloseAt: number
    enableQuickAutoOffset: boolean
    quickAutoOffsetValue: number
    showArea: boolean
    percentAmount: number
  }

  interface ChartData {
    chart_id: string
    settings: ChartSettings
  }
}

export {}
