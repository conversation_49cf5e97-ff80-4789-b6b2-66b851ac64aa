import { MarketData, TradeSignal } from './../trader/SignalGenerator'
import { io, Socket } from 'socket.io-client'
import { WSConnectionState } from '../../../types/websocket/enums'
import WebSocketAuth from './WebSocketAuth'
import { Logger } from '../../../shared/Logger'
import { formatData } from '../../../utils/data'
import { BrowserWindow } from 'electron'
import { extractChartPeriod, extractTimePeriod, toTimeframeOffset } from '../../../utils/parser'
import { SignalGenerator } from '../trader/SignalGenerator'

const logger = new Logger('WS')

class WebSocketClient {
  private static instance: WebSocketClient
  private socket: Socket | null = null
  private auth: WebSocketAuth | null = null

  private readonly SOCKET_URL = 'wss://demo-api-eu.po.market'
  private readonly SOCKET_ORIGIN = 'https://pocketoption.com'
  private readonly HEARTBEAT_TIMEOUT = 20000 // 20 seconds

  private heartbeatTimer: NodeJS.Timeout | null = null
  private chartSettings: ChartData = {} as ChartData
  private selectedAsset: string = ''
  private isGeneratingSignal: boolean = false

  private currentState: WSConnectionState = WSConnectionState.DISCONNECTED

  // Add new properties for candle tracking
  private currentCandle: {
    timestamp: number
    open: number
    high: number
    low: number
    close: number
    startTime: number
    endTime: number
  } | null = null
  private priceHistory: Array<[number, number]> = []
  private candleHistory: Array<[number, number, number, number, number]> = []
  private autoTradeEnabled: boolean = false
  private historicalMarketData: MarketData | null = null

  constructor() {
    this.auth = WebSocketAuth.getInstance()

    this.connect()
  }

  public static getInstance(): WebSocketClient {
    if (!WebSocketClient.instance) {
      WebSocketClient.instance = new WebSocketClient()
    }
    return WebSocketClient.instance
  }

  connect(): void {
    if (this.currentState === WSConnectionState.CONNECTED) return

    this.setConnectionState(WSConnectionState.CONNECTING)

    logger.info(`Connecting to WebSocket server...`)

    const options = {
      transports: ['websocket'],
      query: {
        EIO: '4',
        transport: ['websocket']
      }
    }

    try {
      this.socket = io(this.SOCKET_URL, {
        ...options,
        extraHeaders: {
          Origin: this.SOCKET_ORIGIN
        },
        path: '/socket.io/'
      })

      this.setupEventHandlers()
    } catch (error) {
      logger.debug(`Error connecting to WebSocket server: ${error}`)
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.stopHeartbeat()
      this.socket.disconnect()
      this.socket = null

      // If disconnect is called somewhere else, we need to update the state
      this.setConnectionState(WSConnectionState.DISCONNECTED)
      logger.info('Disconnected from WebSocket server')
    }
  }

  emit(event: string, ...args: unknown[]): void {
    if (!this.socket || !this.socket.connected) {
      logger.warn('Socket is not connected')
      return
    }

    const data = args.length === 1 ? args[0] : args

    if (data === undefined) {
      this.socket.emit(event)
    } else {
      const formattedData = formatData(data)
      logger.info(`Emitting event: ${event} with data: ${JSON.stringify(formattedData, null, 2)}`)
      this.socket.emit(event, formattedData)
    }
  }

  selectAsset(assetSymbol: string, generateSignal?: boolean): void {
    const requestID = Date.now()
    const timeframe = this.getChartTimeframe() ?? 'M1'
    const timePeriod = extractTimePeriod(timeframe)
    const offset = toTimeframeOffset(timeframe)

    // Clear previous data if switching to a different asset
    if (this.selectedAsset && this.selectedAsset !== assetSymbol) {
      logger.info(`Switching from ${this.selectedAsset} to ${assetSymbol}, clearing history`)
      this.priceHistory = []
      this.candleHistory = []
      this.currentCandle = null
      this.historicalMarketData = null
    }

    this.isGeneratingSignal = generateSignal || false

    logger.info(`Selected asset: ${assetSymbol} with timeframe: ${timeframe}`, {
      requestID,
      timeframe,
      timePeriod,
      offset
    })
    this.selectedAsset = assetSymbol

    if (this.socket && this.socket.connected) {
      console.log(`PAYLOAD`, {
        asset: assetSymbol,
        index: requestID,
        offset: offset,
        period: timePeriod,
        time: Math.floor(new Date().getTime() / 1000)
      })
      this.socket.emit('changeSymbol', { asset: assetSymbol, period: timePeriod })
      this.socket.emit('loadHistoryPeriod', {
        asset: assetSymbol,
        index: requestID,
        offset: offset,
        period: timePeriod,
        time: Math.floor(new Date().getTime() / 1000)
      })

      // PAYLOAD {
      // 	"asset": "AUDUSD_otc",
      // 	"index": 1750700445,
      // 	"time": 1750700445,
      // 	"offset": 6000,
      // 	"period": 30
      // }
    }
  }

  getSelectedAsset(): string {
    return this.selectedAsset
  }

  private getChartTimeframe(): string | undefined {
    if (!this.chartSettings || !this.chartSettings.settings) {
      logger.debug(`Chart settings not set`)
      return undefined
    }

    const timeframe = extractChartPeriod(this.chartSettings.settings.chartPeriod)

    // logger.info(
    //   `Extracted chart timeframe from chart period [${this.chartSettings.settings.chartPeriod}]: ${timeframe}`
    // )
    return timeframe
  }
  private broadcast(event: string, data?: unknown): void {
    let payload: unknown

    BrowserWindow.getAllWindows().forEach((window) => {
      if (data instanceof Buffer || data instanceof ArrayBuffer) {
        payload = formatData(data)
      } else {
        payload = data
      }
      window.webContents.send('ws:event', event, payload)
    })
  }
  private setConnectionState(state: WSConnectionState): void {
    this.currentState = state
  }

  private setupEventHandlers(): void {
    if (!this.socket) return

    this.socket.on('connect', this.handleConnect)
    this.socket.on('disconnect', this.handleDisconnect)
    this.socket.on('successauth', this.handleSuccessAuth)
    this.socket.on('updateCharts', this.handleUpdateCharts)
    this.socket.on('updateHistoryNew', this.handleUpdateHistoryNew)
    this.socket.on('loadHistoryPeriodFast', this.handleHistoryPeriodFast)
    this.socket.on('updateStream', this.handleUpdateStream)

    this.socket.onAny((event: string, ...args: unknown[]) => {
      const data = args.length === 1 ? args[0] : args
      const formattedData = formatData(data)
      logger.info(`Received event: ${event}`)

      // if (event.includes('history')) {
      //   logger.info(`Received event: ${event} ${JSON.stringify(formattedData)}`)
      // }

      // loadHistoryPeriodFast
      if (event === 'updateHistoryNewFast') {
        logger.info(`Received event: ${event} ${JSON.stringify(formattedData)}`)
      }
    })
  }

  private startHeartbeat(): void {
    this.stopHeartbeat()
    this.heartbeatTimer = setInterval(() => {
      if (this.socket && this.socket.connected) {
        this.socket.emit('ps')
        logger.info(`Heartbeat sent`)
      }
    }, this.HEARTBEAT_TIMEOUT)
  }

  private stopHeartbeat(): void {
    if (!this.heartbeatTimer) return

    clearInterval(this.heartbeatTimer)
    this.heartbeatTimer = null
    logger.info(`Heartbeat stopped`)
  }

  //#region Event handlers
  // ===============================================================

  private handleConnect = (): void => {
    try {
      if (!this.socket || !this.auth) {
        throw new Error('Socket or auth is not defined')
      }

      this.socket.emit('auth', this.auth.getAuthPayload())
    } catch (error) {
      logger.debug(`Error connecting to WebSocket server: ${error}`)
    }
  }

  private handleSuccessAuth = (): void => {
    this.setConnectionState(WSConnectionState.CONNECTED)
    this.startHeartbeat()
    this.broadcast(`connected`)
    logger.success('Connected to WebSocket server')
  }

  private handleDisconnect = (): void => {
    this.setConnectionState(WSConnectionState.DISCONNECTED)
    this.disconnect()
    this.broadcast(`disconnected`)
    logger.info('Disconnected from WebSocket server')
  }

  /**
   * For now, we only support one chart at a time
   * @param data
   */
  private handleUpdateCharts = (data: unknown): void => {
    const formatted = formatData(data)
    if (Array.isArray(formatted) && formatted.length === 1) {
      const chartItem = formatted[0]
      const settings =
        typeof chartItem.settings === 'string' ? JSON.parse(chartItem.settings) : chartItem.settings

      this.chartSettings = {
        chart_id: chartItem.chart_id || chartItem.chartId || '',
        settings: settings
      }
    } else {
      const chartData = formatted as ChartData
      const settings =
        typeof chartData.settings === 'string' ? JSON.parse(chartData.settings) : chartData.settings

      this.chartSettings = {
        chart_id: chartData.chart_id || '',
        settings: settings
      }
    }
  }

  /**
   * Triggers when emitting `changeSymbol`
   * @param data
   */
  private handleUpdateHistoryNew = (data: unknown): void => {
    const formatted = formatData(data) as { history: unknown[]; candles: unknown[] }
    logger.info(
      `Received updateHistoryNew: ${formatted.history.length} with candles ${formatted.candles.length}`
    )
  }

  private handleHistoryPeriodFast = (data: unknown): void => {
    const formatted = formatData(data) as {
      asset: string
      index: number
      data: Array<{
        symbol_id: number
        time: number
        open: number
        close: number
        high: number
        low: number
      }>
      period: number
    }

    logger.success(
      `Received loadHistoryPeriodFast: ${formatted.asset} with ${formatted.data.length} candles`
    )

    // Only process if it's the selected asset
    if (formatted.asset !== this.selectedAsset) return

    // Clear previous data since we're loading fresh history
    this.priceHistory = []
    this.candleHistory = []
    this.currentCandle = null

    // Process historical candles
    formatted.data.forEach((candle) => {
      // Add to candle history in the expected format: [timestamp, open, high, low, close]
      this.candleHistory.push([candle.time, candle.open, candle.high, candle.low, candle.close])

      // Add close price to price history: [timestamp, price]
      this.priceHistory.push([candle.time, candle.close])
    })

    logger.info(
      `Initialized history with ${this.candleHistory.length} candles and ${this.priceHistory.length} price points`
    )

    // Store the market data for signal generation
    this.historicalMarketData = {
      asset: formatted.asset,
      period: formatted.period,
      history: this.priceHistory,
      candles: this.candleHistory
    }

    // Generate initial signal if requested
    if (this.isGeneratingSignal && this.historicalMarketData) {
      this.broadcast('trade:generating-signal')
      const result = this.generateSignal(this.historicalMarketData)
      logger.warn(`Generated signal from historical data: ${JSON.stringify(result, null, 2)}`)

      // Process the signal
      this.processTradeSignal(result)

      // Calculate next candle close time
      const currentTime = Math.floor(Date.now() / 1000)
      const nextCandleStart =
        Math.floor(currentTime / formatted.period) * formatted.period + formatted.period
      this.broadcast('trade:candle-close', {
        nextCloseTime: nextCandleStart * 1000
      })

      this.isGeneratingSignal = false
    }
  }

  /**
   * Handle real-time price updates and detect candle closes
   */
  private handleUpdateStream = (data: unknown): void => {
    const formatted = formatData(data) as Array<[string, number, number]>

    if (!Array.isArray(formatted) || formatted.length === 0) return

    const [asset, timestamp, price] = formatted[0]

    // Only process if it's the selected asset
    if (asset !== this.selectedAsset) return

    const timeframe = this.getChartTimeframe()
    if (!timeframe) {
      logger.warn('No timeframe set, cannot track candles')
      return
    }

    // Convert timeframe to seconds
    const periodSeconds = extractTimePeriod(timeframe)

    // Calculate candle boundaries
    const candleStartTime = Math.floor(timestamp / periodSeconds) * periodSeconds
    const candleEndTime = candleStartTime + periodSeconds

    // Check if this is a new candle
    if (!this.currentCandle || this.currentCandle.startTime !== candleStartTime) {
      // Previous candle has closed
      if (this.currentCandle) {
        this.onCandleClose(this.currentCandle)
      }

      // Start new candle
      this.currentCandle = {
        timestamp: candleStartTime,
        open: price,
        high: price,
        low: price,
        close: price,
        startTime: candleStartTime,
        endTime: candleEndTime
      }
    } else {
      // Update current candle
      this.currentCandle.high = Math.max(this.currentCandle.high, price)
      this.currentCandle.low = Math.min(this.currentCandle.low, price)
      this.currentCandle.close = price
    }

    // Add to price history
    this.priceHistory.push([timestamp, price])

    // Keep only necessary history (e.g., last 1000 points)
    if (this.priceHistory.length > 1000) {
      this.priceHistory.shift()
    }
  }

  /**
   * Called when a candle closes
   */
  private onCandleClose(candle: typeof this.currentCandle): void {
    if (!candle) return

    logger.info(
      `Candle closed: ${JSON.stringify({
        time: new Date(candle.timestamp * 1000).toISOString(),
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close
      })}`
    )

    // Check if this candle already exists in history (from historical data)
    const existingCandleIndex = this.candleHistory.findIndex((c) => c[0] === candle.timestamp)

    if (existingCandleIndex !== -1) {
      // Update existing candle with potentially more accurate data
      this.candleHistory[existingCandleIndex] = [
        candle.timestamp,
        candle.open,
        candle.high,
        candle.low,
        candle.close
      ]
      logger.info(`Updated existing candle at timestamp ${candle.timestamp}`)
    } else {
      // Add new candle to history
      this.candleHistory.push([
        candle.timestamp,
        candle.open,
        candle.high,
        candle.low,
        candle.close
      ])

      // Keep only necessary history (e.g., last 200 candles)
      if (this.candleHistory.length > 200) {
        this.candleHistory.shift()
      }
    }

    logger.info(`Total candles in history: ${this.candleHistory.length}`)

    // Calculate next candle close time
    if (candle.endTime) {
      this.broadcast('trade:candle-close', {
        nextCloseTime: candle.endTime * 1000 // Convert to milliseconds
      })
    }

    // Generate signal if auto trading is enabled
    if (this.autoTradeEnabled) {
      const timeframe = this.getChartTimeframe() ?? 'M1'
      const period = extractTimePeriod(timeframe)

      // Notify UI that signal generation is starting
      this.broadcast('trade:generating-signal')

      // Combine historical and real-time data
      const marketData: MarketData = {
        asset: this.selectedAsset,
        period,
        history: this.priceHistory,
        candles: this.candleHistory
      }

      logger.info(
        `Generating signal with ${marketData.history.length} price points and ${marketData.candles.length} candles`
      )
      const signal = this.generateSignal(marketData)

      // Always broadcast the signal, including HOLD signals
      this.processTradeSignal(signal)
    }
  }

  /**
   * Process trade signal and potentially open a trade
   */
  private processTradeSignal(signal: TradeSignal): void {
    logger.success(`Trade Signal Generated: ${JSON.stringify(signal, null, 2)}`)

    // Broadcast signal to renderer for UI update
    this.broadcast('trade:signal', signal)

    // TODO: Implement actual trade execution logic here
    // This would involve calling the broker's API to place a trade
    // For now, just log the signal

    if (signal.confidence > 70) {
      logger.warn(
        `${signal.type.toUpperCase()} High confidence signal! Consider opening ${signal.type} trade`
      )
      // this.executeTrade(signal)
    }
  }

  /**
   * Enable or disable automated trading
   */
  public setAutoTrading(enabled: boolean): void {
    this.autoTradeEnabled = enabled
    logger.info(`Auto trading ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Get current trading status
   */
  public getTradingStatus(): {
    autoTradeEnabled: boolean
    currentAsset: string
    timeframe: string | undefined
    candleCount: number
    priceCount: number
  } {
    return {
      autoTradeEnabled: this.autoTradeEnabled,
      currentAsset: this.selectedAsset,
      timeframe: this.getChartTimeframe(),
      candleCount: this.candleHistory.length,
      priceCount: this.priceHistory.length
    }
  }

  /**
   * Start the trading bot for a specific asset
   */
  public startBot(assetSymbol: string): void {
    logger.info(`Starting bot for asset: ${assetSymbol}`)

    // Enable auto trading
    this.setAutoTrading(true)

    // Select the asset which will trigger loadHistoryPeriodFast and generate initial signal
    this.selectAsset(assetSymbol, true)

    logger.success(`Bot started for ${assetSymbol}`)
  }

  /**
   * Stop the trading bot
   */
  public stopBot(): void {
    logger.info('Stopping bot')
    this.setAutoTrading(false)
    logger.success('Bot stopped')
  }

  // ===============================================================
  //#endregion Event handlers

  private generateSignal = (marketData: MarketData): TradeSignal => {
    const signalGenerator = new SignalGenerator()
    const signal = signalGenerator.generateSignal(marketData)

    logger.success(`Generated signal from function: ${JSON.stringify(signal, null, 2)}`)
    return signal
  }
}

export default WebSocketClient
