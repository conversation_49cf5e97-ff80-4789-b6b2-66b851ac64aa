import { RSISettings, RSIResult } from '../../../../types/trading'

class RsiIndicator {
  private settings: RSISettings
  private priceHistory: number[] = []
  private lastRSI: number | null = null

  constructor(settings: Partial<RSISettings> = {}) {
    this.settings = {
      period: 14,
      overbought: 70,
      oversold: 30,
      ...settings
    }
  }

  calculate = (prices: number[]): RSIResult | null => {
    if (prices.length < this.settings.period + 1) {
      return null // Need at least period + 1 data points
    }

    // Calculate price changes
    const changes = this.calculatePriceChanges(prices)

    // Calculate gains and losses
    const gains = changes.map((change) => (change > 0 ? change : 0))
    const losses = changes.map((change) => (change < 0 ? Math.abs(change) : 0))

    // Calculate average gain and loss
    const avgGain = this.calculateAverage(gains.slice(-this.settings.period))
    const avgLoss = this.calculateAverage(losses.slice(-this.settings.period))

    if (avgLoss === 0) {
      return {
        rsi: 100,
        signal: 'SELL',
        strength: 'STRONG'
      }
    }

    // Calculate RS
    const rs = avgLoss !== 0 ? avgGain / avgLoss : 0

    // Calculate RSI
    const rsi = 100 - 100 / (1 + rs)

    this.lastRSI = rsi

    return {
      rsi: Number(rsi.toFixed(2)),
      signal: this.generateSignal(rsi),
      strength: this.calculateStrength(rsi)
    }
  }

  update = (newPrice: number): RSIResult | null => {
    this.priceHistory.push(newPrice)

    if (this.priceHistory.length > this.settings.period + 50) {
      this.priceHistory = this.priceHistory.slice(-this.settings.period - 20)
    }

    return this.calculate(this.priceHistory)
  }

  getLastRSI = (): number | null => {
    return this.lastRSI
  }

  getSettings = (): RSISettings => {
    return this.settings
  }

  reset = (): void => {
    this.priceHistory = []
    this.lastRSI = null
  }

  /**
   * Advanced analysis
   * Check for bullish and bearish divergences
   */
  checkDivergence = (
    prices: number[],
    rsiValues: number[]
  ): {
    bullishDivergence: boolean
    bearishDivergence: boolean
  } => {
    if (prices.length < 10 || rsiValues.length < 10) {
      return { bullishDivergence: false, bearishDivergence: false }
    }

    const lastPrices = prices.slice(-5) // prices[prices.length - 1]
    const lastRSI = rsiValues.slice(-5) // rsiValues[rsiValues.length - 1]

    const priceUptrend = lastPrices[4] > lastPrices[0]
    const priceDowntrend = lastPrices[4] < lastPrices[0]

    const rsiUptrend = lastRSI[4] > lastRSI[0]
    const rsiDowntrend = lastRSI[4] < lastRSI[0]

    const bullishDivergence = priceDowntrend && rsiUptrend // Price falling but RSI rising
    const bearishDivergence = priceUptrend && rsiDowntrend // Price rising but RSI falling

    return { bullishDivergence, bearishDivergence }
  }

  /**
   * Calculate price changes between consecutive prices
   * @param prices Array of prices
   * @returns
   */
  private calculatePriceChanges = (prices: number[]): number[] => {
    const changes: number[] = []

    for (let i = 1; i < prices.length; i++) {
      changes.push(prices[i] - prices[i - 1])
    }

    return changes
  }

  private calculateAverage = (values: number[]): number => {
    if (values.length === 0) return 0
    return values.reduce((a, b) => a + b, 0) / values.length
  }

  private calculateStrength = (rsi: number): 'WEAK' | 'MODERATE' | 'STRONG' => {
    const distanceFromOverbought = Math.abs(rsi - this.settings.overbought)
    const distanceFromOversold = Math.abs(rsi - this.settings.oversold)
    const minDistance = Math.min(distanceFromOverbought, distanceFromOversold)

    if (minDistance <= 5) {
      return 'STRONG'
    } else if (minDistance <= 15) {
      return 'MODERATE'
    } else {
      return 'WEAK'
    }
  }

  private generateSignal = (rsi: number): 'BUY' | 'SELL' | 'HOLD' => {
    if (rsi <= this.settings.oversold) {
      return 'BUY'
    } else if (rsi >= this.settings.overbought) {
      return 'SELL'
    } else {
      return 'HOLD'
    }
  }
}

export default RsiIndicator
