import { Logger } from './../shared/Logger'

const logger = new Logger('UTILS')

/**
 * Format and parse data from various sources
 */
export const formatData = (payload: unknown): unknown | null => {
  try {
    if (payload instanceof Buffer || payload instanceof ArrayBuffer) {
      const buffer = payload as Buffer
      const jsonStr = buffer.toString('utf-8')
      const jsonData = JSON.parse(jsonStr)
      return jsonData
    }

    if (typeof payload === 'string') {
      try {
        return JSON.parse(payload)
      } catch {
        return payload // Return as string if not valid JSON
      }
    }

    return payload
  } catch (error) {
    logger.debug(`Error formatting payload: ${error}`)
    return null
  }
}
