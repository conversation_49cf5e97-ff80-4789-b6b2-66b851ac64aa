/**
 * Extract time period from the time period string (e.g., 'M1' -> 1, 'S30' -> 30)
 * @param timePeriod - The time period string to extract the time period from
 * @returns The time period in seconds
 */
export const extractTimePeriod = (timePeriod: string): number => {
  const timeValue = parseInt(timePeriod.slice(1), 10)
  const periodType = timePeriod.charAt(0)

  switch (periodType) {
    case 'M':
      return timeValue * 60
    case 'S':
      return timeValue
    case 'H':
      return timeValue * 3600
    default:
      console.error('Invalid time period format')
      return 0
  }
}

/**
 * Extracts the chart period from the ChartSettings
 * @param chartPeriod - The chart period to extract the time period from
 * @returns
 */
export function extractChartPeriod(chartPeriod: number): string | undefined {
  const chartPeriodMap: Record<number, string> = {
    0: `S5`,
    1: `S10`,
    2: `S15`,
    3: `S30`,
    4: `M1`,
    13: `M2`,
    14: `M3`,
    6: `M5`,
    7: `M10`,
    8: `M15`,
    9: `M30`,
    10: `H1`,
    11: `H4`,
    12: `D1`
  }

  return chartPeriodMap[chartPeriod]
}

/**
 * Converts a timeframe to an offset
 * @param timeframe - The timeframe to convert to an offset (e.g., 'M1' -> 9000)
 * @returns
 */
export const toTimeframeOffset = (timeframe: string | number): string | number | undefined => {
  const timeframeMap = {
    S5: 1000,
    S10: 2000,
    S15: 3000,
    S30: 6000,
    M1: 9000,
    M2: 18000,
    M3: 27000,
    M5: 45000,
    M10: 90000,
    M15: 135000,
    M30: 270000,
    H1: 540000,
    H4: 2160000,
    D1: 12960000
  }
  if (typeof timeframe === 'string') return timeframeMap[timeframe]
  for (const [key, value] of Object.entries(timeframeMap)) {
    if (value === timeframe) return key
  }
  return undefined
}
