import { SignalGenerator, MarketData } from './SignalGenerator'

// Example usage with your provided data
const marketData: MarketData = {
  asset: 'USDJPY_otc',
  period: 60,
  history: [
    // Your history data here...
    [1750694033.672, 139.512],
    [1750694034.128, 139.512]
    // ... (truncated for brevity)
  ],
  candles: [
    // Your candles data here...
    [1750694460, 139.366, 139.347, 139.379, 139.315]
    // ... (truncated for brevity)
  ]
}

// Create an instance of SignalGenerator
const signalGenerator = new SignalGenerator()

// Generate a signal with automatic duration recommendation
const signal = signalGenerator.generateSignal(marketData)
console.log('Generated Signal:', signal)

// Generate a signal with custom duration (e.g., 300 seconds = 5 minutes)
const signalWithCustomDuration = signalGenerator.generateSignal(marketData, 300)
console.log('Signal with custom duration:', signalWithCustomDuration)

// Get detailed MACD analysis
const macdAnalysis = signalGenerator.getMACDAnalysis(marketData)
console.log('MACD Analysis:', macdAnalysis)

// Example output interpretation:
if (signal.type === 'BUY') {
  console.log(`
    📈 BUY Signal Generated!
    Price: ${signal.price}
    Confidence: ${signal.confidence.toFixed(2)}%
    Suggested Duration: ${signal.suggestedDuration} seconds
    Reason: ${signal.reason}
  `)
} else if (signal.type === 'SELL') {
  console.log(`
    📉 SELL Signal Generated!
    Price: ${signal.price}
    Confidence: ${signal.confidence.toFixed(2)}%
    Suggested Duration: ${signal.suggestedDuration} seconds
    Reason: ${signal.reason}
  `)
} else {
  console.log(`
    ⏸️ HOLD Position
    Current Price: ${signal.price}
    Reason: ${signal.reason}
  `)
}
